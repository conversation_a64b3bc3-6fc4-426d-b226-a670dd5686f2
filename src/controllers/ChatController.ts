import { Request, Response } from 'express';
import { ChatService } from '../services/ChatService';
import { LLMService } from '../services/LLMService';
import { ResponseUtil } from '../utils/response';
import { SUCCESS_MESSAGES, ERROR_MESSAGES } from '../utils/constants';
import { ChatRequest, AuthenticatedRequest, SimpleChatRequest, RegenerateRequest } from '../types';
import { asyncHandler } from '../middleware/errorHandler';
import { EncryptionUtil } from '../utils/encryption';
import logger from '../config/logger';
import { FileProcessingService, FileAttachment } from '../services/FileProcessingService';

export class ChatController {
  /**
   * Send chat message (authenticated or guest)
   */
  static sendMessage = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    const chatRequest: ChatRequest = req.body;
    const user = req.user;
    const sessionId = (req as any).sessionId || chatRequest.sessionId;
    const clientIP = (req as any).clientIP || '127.0.0.1';

    if (user) {
      // Authenticated user chat
      // Ensure sessionId is set in chatRequest for conversation continuity
      if (sessionId && !chatRequest.sessionId) {
        chatRequest.sessionId = sessionId;
      }
      const result = await ChatService.processUserChat(user.userId, chatRequest);
      ResponseUtil.success(res, SUCCESS_MESSAGES.MESSAGE_SENT, result);
    } else {
      // Guest user chat
      if (!sessionId) {
        // Generate new session ID for guest
        const newSessionId = EncryptionUtil.generateSessionId();
        chatRequest.sessionId = newSessionId;
      } else {
        chatRequest.sessionId = sessionId;
      }

      const result = await ChatService.processGuestChat(
        chatRequest.sessionId!,
        chatRequest,
        clientIP
      );
      ResponseUtil.success(res, SUCCESS_MESSAGES.MESSAGE_SENT, result);
    }
  });

  /**
   * Send streaming chat message (authenticated or guest)
   */
  static sendStreamingMessage = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    const chatRequest: ChatRequest = req.body;
    const user = req.user;
    const sessionId = (req as any).sessionId || chatRequest.sessionId;
    const clientIP = (req as any).clientIP || '127.0.0.1';

    try {
      if (user) {
        // Authenticated user chat
        // Ensure sessionId is set in chatRequest for conversation continuity
        if (sessionId && !chatRequest.sessionId) {
          chatRequest.sessionId = sessionId;
        }
        const result = await ChatService.processUserChatStreaming(user.userId, chatRequest, res);
        // Note: Response is handled by the streaming service, no need to send additional response
      } else {
        // Guest user chat
        if (!sessionId) {
          // Generate new session ID for guest
          const newSessionId = EncryptionUtil.generateSessionId();
          chatRequest.sessionId = newSessionId;
        } else {
          chatRequest.sessionId = sessionId;
        }

        const result = await ChatService.processGuestChatStreaming(
          chatRequest.sessionId!,
          chatRequest,
          clientIP,
          res
        );
        // Note: Response is handled by the streaming service, no need to send additional response
      }
    } catch (error) {
      logger.error('Error in streaming chat:', error);

      // Send error event if response hasn't been sent yet
      if (!res.headersSent) {
        res.writeHead(200, {
          'Content-Type': 'text/event-stream',
          'Cache-Control': 'no-cache',
          'Connection': 'keep-alive',
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Headers': 'Cache-Control',
        });

        res.write(`data: ${JSON.stringify({
          type: 'error',
          error: error instanceof Error ? error.message : 'Unknown error',
          timestamp: new Date().toISOString()
        })}\n\n`);
        res.end();
      }
    }
  });

  /**
   * Simplified streaming chat message handler
   * Removes complexity of session ID extraction, CSRF tokens, and IP tracking
   */
  static sendSimpleStreamingMessage = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    const chatRequest: SimpleChatRequest = req.body;
    const user = req.user;

    try {
      if (user) {
        // Authenticated user - simple flow
        const result = await ChatService.processSimpleUserChatStreaming(user.userId, chatRequest, res);
      } else {
        // Guest user - simple flow without IP tracking
        const result = await ChatService.processSimpleGuestChatStreaming(chatRequest, res);
      }
    } catch (error) {
      logger.error('Error in simple streaming chat:', error);

      // Send error event if response hasn't been sent yet
      if (!res.headersSent) {
        res.writeHead(200, {
          'Content-Type': 'text/event-stream',
          'Cache-Control': 'no-cache',
          'Connection': 'keep-alive',
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Headers': 'Cache-Control',
        });

        res.write(`data: ${JSON.stringify({
          type: 'error',
          error: error instanceof Error ? error.message : 'Unknown error',
          timestamp: new Date().toISOString()
        })}\n\n`);
        res.end();
      }
    }
  });

  /**
   * Get user chats (authenticated only)
   */
  static getUserChats = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    if (!req.user) {
      ResponseUtil.unauthorized(res, ERROR_MESSAGES.UNAUTHORIZED);
      return;
    }

    const limit = parseInt(req.query.limit as string) || 20;
    const offset = parseInt(req.query.offset as string) || 0;

    const chats = await ChatService.getUserChats(req.user.userId, limit, offset);
    ResponseUtil.success(res, 'Chats retrieved successfully', { chats, limit, offset });
  });

  /**
   * Get chat messages
   */
  static getChatMessages = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    const { chatId } = req.params;
    const limit = parseInt(req.query.limit as string) || 50;
    const offset = parseInt(req.query.offset as string) || 0;

    const messages = await ChatService.getChatMessages(
      chatId,
      req.user?.userId,
      limit,
      offset
    );

    ResponseUtil.success(res, 'Messages retrieved successfully', {
      messages,
      chatId,
      limit,
      offset
    });
  });

  /**
   * Delete chat (authenticated only)
   */
  static deleteChat = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    if (!req.user) {
      ResponseUtil.unauthorized(res, ERROR_MESSAGES.UNAUTHORIZED);
      return;
    }

    const { chatId } = req.params;

    await ChatService.deleteChat(chatId, req.user.userId);
    ResponseUtil.success(res, 'Chat deleted successfully');
  });

  /**
   * Get guest session info
   */
  static getGuestSessionInfo = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    const sessionId =
      req.headers['x-session-id'] as string ||
      req.query.sessionId as string ||
      req.params.sessionId as string;

    if (!sessionId) {
      ResponseUtil.validationError(res, 'Session ID is required');
      return;
    }

    const sessionInfo = await ChatService.getGuestSessionInfo(sessionId);
    ResponseUtil.success(res, 'Session info retrieved successfully', sessionInfo);
  });

  /**
   * Convert guest chat to user chat (when user logs in)
   */
  static convertGuestChat = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    if (!req.user) {
      ResponseUtil.unauthorized(res, ERROR_MESSAGES.UNAUTHORIZED);
      return;
    }

    const { sessionId } = req.body;

    if (!sessionId) {
      ResponseUtil.validationError(res, 'Session ID is required');
      return;
    }

    await ChatService.convertGuestChatToUser(sessionId, req.user.userId);
    ResponseUtil.success(res, 'Guest chat converted successfully');
  });

  /**
   * Get available LLM models
   */
  static getAvailableModels = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    const models = LLMService.getAvailableModels();
    const stats = LLMService.getModelStats();

    ResponseUtil.success(res, 'Available models retrieved successfully', {
      models,
      stats
    });
  });

  /**
   * Test LLM model
   */
  static testModel = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    const { model, message } = req.body;

    if (!message) {
      ResponseUtil.validationError(res, 'Message is required');
      return;
    }

    try {
      const response = await LLMService.generateResponse(
        message,
        model,
        'You are a helpful assistant. Respond briefly to test the model.'
      );

      ResponseUtil.success(res, 'Model test completed', {
        model: model || 'default',
        message,
        response,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      logger.error('Model test error:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      ResponseUtil.error(res, 'Model test failed', errorMessage);
    }
  });

  /**
   * Get chat statistics (admin endpoint)
   */
  static getChatStats = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    const stats = await ChatService.getChatStatistics();
    ResponseUtil.success(res, 'Chat statistics retrieved successfully', stats);
  });

  /**
   * Search chats (authenticated only)
   */
  static searchChats = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    if (!req.user) {
      ResponseUtil.unauthorized(res, ERROR_MESSAGES.UNAUTHORIZED);
      return;
    }

    const { query } = req.query;
    const limit = parseInt(req.query.limit as string) || 20;
    const offset = parseInt(req.query.offset as string) || 0;

    if (!query || typeof query !== 'string') {
      ResponseUtil.validationError(res, 'Search query is required');
      return;
    }

    // This is a simplified search - in production you might use full-text search
    const { Chat } = await import('../models/chat');
    const { Op } = await import('sequelize');

    const chats = await Chat.findAll({
      where: {
        userId: req.user.userId,
        isGuest: false,
        title: {
          [Op.like]: `%${query}%`
        }
      },
      order: [['updatedAt', 'DESC']],
      limit,
      offset
    });

    ResponseUtil.success(res, 'Search completed', {
      chats,
      query,
      limit,
      offset
    });
  });

  /**
   * Update chat title
   */
  static updateChatTitle = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    if (!req.user) {
      ResponseUtil.unauthorized(res, ERROR_MESSAGES.UNAUTHORIZED);
      return;
    }

    const { chatId } = req.params;
    const { title } = req.body;

    if (!title || typeof title !== 'string' || title.trim().length === 0) {
      ResponseUtil.validationError(res, 'Valid title is required');
      return;
    }

    const { Chat } = await import('../models/chat');
    const chat = await Chat.findByPk(chatId);

    if (!chat || chat.userId !== req.user.userId) {
      ResponseUtil.notFound(res, ERROR_MESSAGES.CHAT_NOT_FOUND);
      return;
    }

    await chat.updateTitle(title.trim());
    ResponseUtil.success(res, 'Chat title updated successfully', {
      chatId,
      title: title.trim()
    });
  });

  /**
   * Export chat history
   */
  static exportChat = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    if (!req.user) {
      ResponseUtil.unauthorized(res, ERROR_MESSAGES.UNAUTHORIZED);
      return;
    }

    const { chatId } = req.params;
    const format = req.query.format as string || 'json';

    const messages = await ChatService.getChatMessages(chatId, req.user.userId);
    const { Chat } = await import('../models/chat');
    const chat = await Chat.findByPk(chatId);

    if (!chat) {
      ResponseUtil.notFound(res, ERROR_MESSAGES.CHAT_NOT_FOUND);
      return;
    }

    const exportData = {
      chat: {
        id: chat.id,
        title: chat.title,
        createdAt: chat.createdAt,
        updatedAt: chat.updatedAt
      },
      messages: messages.map(msg => ({
        id: msg.id,
        message: msg.message,
        response: msg.response,
        llmModel: msg.llmModel,
        createdAt: msg.createdAt
      })),
      exportedAt: new Date().toISOString()
    };

    if (format === 'json') {
      res.setHeader('Content-Type', 'application/json');
      res.setHeader('Content-Disposition', `attachment; filename="chat-${chatId}.json"`);
      res.send(JSON.stringify(exportData, null, 2));
    } else {
      ResponseUtil.validationError(res, 'Unsupported export format');
    }
  });

  /**
   * Regenerate AI response for a specific message
   */
  static regenerateResponse = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    const regenerateRequest: RegenerateRequest = req.body;
    const user = req.user;

    try {
      const result = await ChatService.regenerateResponse(
        regenerateRequest.messageId,
        user?.userId,
        regenerateRequest.llmModel,
        res
      );

      // For streaming responses, the response is handled by the service
      // For non-streaming, we would send the result here, but we're using streaming by default
    } catch (error) {
      logger.error('Error in regenerate response:', error);

      // Send error event if response hasn't been sent yet
      if (!res.headersSent) {
        res.writeHead(200, {
          'Content-Type': 'text/event-stream',
          'Cache-Control': 'no-cache',
          'Connection': 'keep-alive',
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Headers': 'Cache-Control',
        });

        res.write(`data: ${JSON.stringify({
          type: 'error',
          error: error instanceof Error ? error.message : 'Unknown error',
          timestamp: new Date().toISOString()
        })}\n\n`);
        res.end();
      }
    }
  });

  /**
   * Send streaming chat message with file attachment support
   */
  static sendStreamingMessageWithAttachment = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    const chatRequest: SimpleChatRequest = req.body;
    const user = req.user;
    const file = req.file;

    try {
      let processedFile = undefined;

      // Process file if attached
      if (file) {
        const fileAttachment: FileAttachment = {
          buffer: file.buffer,
          originalname: file.originalname,
          mimetype: file.mimetype,
          size: file.size
        };

        processedFile = await FileProcessingService.processFile(fileAttachment);
        logger.info(`File processed successfully: ${file.originalname} (${file.mimetype})`);
      }

      if (user) {
        // Authenticated user - simple flow with file attachment
        const result = await ChatService.processSimpleUserChatStreamingWithAttachment(
          user.userId,
          chatRequest,
          res,
          processedFile
        );
      } else {
        // Guest user - simple flow with file attachment
        const result = await ChatService.processSimpleGuestChatStreamingWithAttachment(
          chatRequest,
          res,
          processedFile
        );
      }
    } catch (error) {
      logger.error('Error in streaming chat with attachment:', error);

      // Send error event if response hasn't been sent yet
      if (!res.headersSent) {
        res.writeHead(200, {
          'Content-Type': 'text/event-stream',
          'Cache-Control': 'no-cache',
          'Connection': 'keep-alive',
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Headers': 'Cache-Control',
        });

        res.write(`data: ${JSON.stringify({
          type: 'error',
          error: error instanceof Error ? error.message : 'Unknown error',
          timestamp: new Date().toISOString()
        })}\n\n`);
        res.end();
      }
    }
  });
}
