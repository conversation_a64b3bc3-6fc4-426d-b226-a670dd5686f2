import { Router } from 'express';
import { ThreadController } from '../controllers/ThreadController';
import { validate, validationSchemas, validateQuery, validateParams } from '../middleware/validation';
import { authenticateToken, rateLimitPerUser } from '../middleware/auth';
import { CSRFProtection } from '../middleware/security';
import { uploadSingleFile, handleFileUploadError } from '../middleware/fileUpload';

const router = Router();

// All thread routes require authentication
router.use(authenticateToken);

// Send message in thread (regular or project)
router.post('/message',
  validate(validationSchemas.threadMessage),
  rateLimitPerUser(20, 60 * 1000), // 20 messages per minute
  CSRFProtection.protect(),
  ThreadController.sendMessage
);

// Send streaming message in thread (regular or project)
// Simplified version without CSRF protection for better streaming experience
router.post('/message/stream',
  validate(validationSchemas.threadMessage),
  rateLimitPerUser(20, 60 * 1000), // 20 messages per minute
  ThreadController.sendStreamingMessage
);

// Send streaming message in thread with file attachment support
router.post('/message/stream/attachment',
  uploadSingleFile,
  handleFileUploadError,
  validate(validationSchemas.threadMessage),
  rateLimitPerUser(15, 60 * 1000), // 15 messages per minute (lower limit for attachments)
  ThreadController.sendStreamingMessageWithAttachment
);

// Get user threads (regular threads only, not project threads)
router.get('/',
  validateQuery(validationSchemas.pagination),
  ThreadController.getUserThreads
);

// Get specific thread details
router.get('/:threadId',
  validateParams(validationSchemas.threadId),
  ThreadController.getThread
);

// Get thread messages
router.get('/:threadId/messages',
  validateParams(validationSchemas.threadId),
  validateQuery(validationSchemas.pagination),
  ThreadController.getThreadMessages
);

// Update thread name
router.put('/:threadId/name',
  validateParams(validationSchemas.threadId),
  validate(validationSchemas.updateThreadName),
  ThreadController.updateThreadName
);

// Delete thread
router.delete('/:threadId',
  validateParams(validationSchemas.threadId),
  ThreadController.deleteThread
);

export default router;
