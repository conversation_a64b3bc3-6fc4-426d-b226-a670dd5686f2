import multer from 'multer';
import { Request, Response, NextFunction } from 'express';
import { ResponseUtil } from '../utils/response';
import logger from '../config/logger';

// Configure multer for memory storage
const storage = multer.memoryStorage();

// File filter to validate file types
const fileFilter = (req: Request, file: Express.Multer.File, cb: multer.FileFilterCallback) => {
  const allowedMimeTypes = [
    // Images
    'image/jpeg',
    'image/jpg',
    'image/png',
    'image/gif',
    'image/webp',
    // Documents
    'application/pdf',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document', // .docx
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
    // Text files
    'text/plain',
    'text/markdown',
    'application/json'
  ];

  // Reject video files explicitly
  if (file.mimetype.startsWith('video/')) {
    logger.warn(`Rejected video file upload: ${file.originalname} (${file.mimetype})`);
    return cb(new Error('Video files are not supported'));
  }

  if (allowedMimeTypes.includes(file.mimetype)) {
    logger.info(`Accepted file upload: ${file.originalname} (${file.mimetype})`);
    cb(null, true);
  } else {
    logger.warn(`Rejected file upload: ${file.originalname} (${file.mimetype})`);
    cb(new Error(`Unsupported file type: ${file.mimetype}`));
  }
};

// Configure multer
const upload = multer({
  storage,
  fileFilter,
  limits: {
    fileSize: 5 * 1024 * 1024, // 5MB limit
    files: 1 // Only allow 1 file per request
  }
});

/**
 * Middleware for handling single file upload
 */
export const uploadSingleFile = upload.single('attachment');

/**
 * Error handling middleware for file upload errors
 */
export const handleFileUploadError = (
  error: any,
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  if (error instanceof multer.MulterError) {
    logger.error('Multer error:', error);
    
    switch (error.code) {
      case 'LIMIT_FILE_SIZE':
        ResponseUtil.validationError(res, 'File size exceeds the maximum limit of 5MB');
        return;
      case 'LIMIT_FILE_COUNT':
        ResponseUtil.validationError(res, 'Only one file can be uploaded at a time');
        return;
      case 'LIMIT_UNEXPECTED_FILE':
        ResponseUtil.validationError(res, 'Unexpected file field. Use "attachment" field name');
        return;
      default:
        ResponseUtil.validationError(res, `File upload error: ${error.message}`);
        return;
    }
  }

  if (error.message) {
    logger.error('File upload error:', error.message);
    ResponseUtil.validationError(res, error.message);
    return;
  }

  next(error);
};

/**
 * Middleware to check if file was uploaded
 */
export const requireFile = (req: Request, res: Response, next: NextFunction): void => {
  if (!req.file) {
    ResponseUtil.validationError(res, 'No file uploaded');
    return;
  }
  next();
};

/**
 * Middleware to make file upload optional
 */
export const optionalFile = (req: Request, res: Response, next: NextFunction): void => {
  // File is optional, continue regardless
  next();
};
